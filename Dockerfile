# 使用 Node.js 18 作为基础镜像
FROM node:22-bullseye-slim

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖，这些是 Puppeteer 运行 Chrome 所需的
RUN apt-get update && apt-get install -y \
    chromium \
    chromium-sandbox \
    fonts-ipafont-gothic \
    fonts-wqy-zenhei \
    fonts-thai-tlwg \
    fonts-kacst \
    fonts-symbola \
    fonts-noto-color-emoji \
    fonts-freefont-ttf \
    libxss1 \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# 设置 Puppeteer 使用系统安装的 Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install --force

# 复制所有源代码
COPY . .

# 构建应用
RUN npm run build

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 暴露端口
EXPOSE 6900

# 启动应用
CMD ["/bin/bash", "-c", "set -e && npm run start"]