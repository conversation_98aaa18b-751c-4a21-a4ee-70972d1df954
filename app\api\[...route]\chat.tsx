import { createOpenAI } from '@ai-sdk/openai';
import { generateText } from 'ai';
import { Hono } from 'hono';
import { promises as fs } from 'fs';
import path from 'path';

const app = new Hono();

// 定义消息角色类型
type MessageRole = 'system' | 'user' | 'assistant';

// 定义消息接口
interface ChatMessage {
    role: MessageRole;
    content: string;
}

// 定义请求体接口
interface RequestBody {
    session_id: string;
    step: number;
    model: string;  // 使用字符串类型，允许任意模型
    prompt: string;
    variables?: Record<string, string>;
}

// 定义错误响应接口
interface ErrorResponse {
    error: boolean;
    code: string;
    message: string;
    session_id?: string;
    step?: number;
    timestamp: string;
}

// 在文件顶部添加接口定义
interface StatsData {
    dailyStats: {
        [key: string]: number;
    };
}

// 在文件顶部添加日志工具函数
function formatLog(level: 'ERROR' | 'INFO' | 'WARN', message: string): string {
    const timestamp = new Date().toISOString();
    return `${timestamp}--${level}--${message}`;
}

// 创建 OpenAI 客户端实例
if (!process.env.OPENAI_API_KEY) {
    const errorMessage = 'API_KEY 未设置';
    console.error(formatLog('ERROR', errorMessage));
    throw new Error('API_KEY 未设置');
}

const openai = createOpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL || 'https://api.mechat.top/v1'
});

// 处理提示词中的变量替换
function processPrompt(prompt: string, variables?: Record<string, string>): string {
    if (!variables) return prompt;
    
    let processedPrompt = prompt;
    Object.entries(variables).forEach(([key, value]) => {
        // 支持两种格式的占位符: {{key}} 和 【key】
        const placeholders = [
            `{{${key}}}`,
            `【${key}】`
        ];
        
        placeholders.forEach(placeholder => {
            processedPrompt = processedPrompt.replace(new RegExp(placeholder, 'g'), value !== undefined && value !== null ? value : placeholder);
        });
    });
    
    return processedPrompt;
}

app.post('', async (c) => {
    try {
        const body = await c.req.json() as RequestBody;
        
        // 记录接收到的请求
        //console.log('Received request:', body);

        // 处理提示词中的变量
        let processedPrompt = processPrompt(body.prompt, body.variables);
        processedPrompt = processedPrompt.replace(/\s/g, ' ');
        // 构建消息
        const message: ChatMessage = {
            role: 'user',
            content: processedPrompt,
        };
        console.log(formatLog('INFO', `Processing message: ${JSON.stringify(message)}`));
        // 创建 AI 请求
        const payload = {
            model: openai(body.model),
            messages: [message],
            temperature: 0.7,
        };
        //console.log(payload)
        const result = await generateText(payload);
        
        // 添加统计代码
        try {
            const today = new Date().toISOString().split('T')[0];
            const statsPath = path.join(process.cwd(), 'data', 'stats.json');
            
            // 读取现有统计数据
            let statsData: StatsData = { dailyStats: {} };
            try {
                const fileContent = await fs.readFile(statsPath, 'utf-8');
                statsData = JSON.parse(fileContent) as StatsData;
            } catch {
                // 如果文件不存在，使用默认的空统计数据
            }
            
            // 更新今天的统计数据
            statsData.dailyStats[today] = (statsData.dailyStats[today] || 0) + 1;
            
            // 写回文件
            await fs.writeFile(statsPath, JSON.stringify(statsData, null, 2));
        } catch (statsError) {
            console.error(formatLog('ERROR', '统计数据更新失败: ' + statsError));
            // 继续处理，不影响主要功能
        }
        
        return Response.json({ result });
        
    } catch (error) {
        console.error(formatLog('ERROR', 'Chat API error: ' + JSON.stringify({
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            timestamp: new Date().toISOString()
        })));

        // 返回格式化的错误响应
        const errorResponse: ErrorResponse = {
            error: true,
            code: (error instanceof Error && 'code' in error) ? 
                  (error as any).code || 'INTERNAL_ERROR' : 'INTERNAL_ERROR',
            message: error instanceof Error ? error.message : 'Unknown error occurred',
            session_id: (error instanceof Error && 'session_id' in error) ? 
                       (error as any).session_id : undefined,
            step: (error instanceof Error && 'step' in error) ? 
                  (error as any).step : undefined,
            timestamp: new Date().toISOString()
        };

        return c.json(errorResponse, (error instanceof Error && 'status' in error) ? 
                     (error as any).status : 500);
    }
});

export default app;