import { Hono } from 'hono'
import tiqu from './tiqu'
import chat from './chat'
import tiqu_xhs from './tiqu_xhs'
import tiqu_toutiao from './tiqu_toutiao'
import tiqu_uc from './tiqu_uc'
import stats from './stats'
import { handle } from 'hono/vercel'

export const runtime = "nodejs";
const app = new Hono().basePath("/api")

app.route('/tiqu', tiqu).route('/chat', chat).route('/tiqu_xhs', tiqu_xhs).route('/tiqu_toutiao', tiqu_toutiao).route('/tiqu_uc', tiqu_uc).route('/stats', stats)

export const GET = handle(app)
export const POST = handle(app)

export type AppType = typeof app