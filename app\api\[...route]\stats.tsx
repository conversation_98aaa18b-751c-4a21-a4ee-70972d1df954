import { Hono } from 'hono';
import { promises as fs } from 'fs';
import path from 'path';

const app = new Hono();

// 格式化日志的函数
function formatLog(level: 'ERROR' | 'INFO' | 'WARN', message: string): string {
    const timestamp = new Date().toISOString();
    return `${timestamp}--${level}--${message}`;
}

// 在文件顶部添加接口定义
interface StatsData {
    dailyStats: {
        [key: string]: number;
    };
}

app.get('', async (c) => {
    try {
        const statsPath = path.join(process.cwd(), 'data', 'stats.json');
        
        // 读取统计数据
        let statsData: StatsData = { dailyStats: {} };
        try {
            const fileContent = await fs.readFile(statsPath, 'utf-8');
            statsData = JSON.parse(fileContent) as StatsData;
        } catch {
            console.warn(formatLog('WARN', '无法读取统计数据文件，返回空统计信息'));
        }
        
        // 返回统计数据
        return c.json({
            success: true,
            data: statsData.dailyStats,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error(formatLog('ERROR', '获取统计数据失败: ' + error));
        return c.json({
            success: false,
            error: '获取统计数据失败',
            timestamp: new Date().toISOString()
        }, 500);
    }
});

export default app;
