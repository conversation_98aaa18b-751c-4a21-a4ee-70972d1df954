import { Hono } from 'hono';
import axios from 'axios';
import crypto from 'crypto';

const app = new Hono();

// 密钥配置
const SECRET_KEY = 'changfengbox.top';

// 生成MD5哈希值
function generateHash(data: string): string {
  return crypto.createHash('md5').update(data).digest('hex');
}

// 生成签名
function generateSign(): { timestamp: number; sign: string } {
  // 获取当前时间戳（秒级）
  const timestamp = Math.floor(Date.now() / 1000);
  
  // 拼接时间戳和密钥
  const signData = `${timestamp}${SECRET_KEY}`;
  
  // 生成哈希签名
  const sign = generateHash(signData);
  
  // 返回签名和时间戳
  return {
    timestamp,
    sign
  };
}

// 从微信文章URL获取内容
async function fetchArticleContent(url: string, format: 'text' | 'markdown' = 'text'): Promise<any> {
  try {
    // 准备请求数据
    const apiUrl = "https://changfengbox.top/api/download/wechat";
    const requestData = {
      url: url,
      config: {
        TXT: true,
      }
    };

    // 生成签名和时间戳
    const signInfo = generateSign();

    // 请求头
    const headers = {
      "Content-Type": "application/json",
      "x-sign": signInfo.sign,
      "x-timestamp": signInfo.timestamp.toString()
    };

    console.log(`正在请求文章: ${url}`);
    console.log(`请求头: ${JSON.stringify(headers)}`);
    console.log(`请求数据: ${JSON.stringify(requestData)}`);

    // 发送POST请求
    const response = await axios.post(apiUrl, requestData, { headers });
    
    // 检查响应
    const responseData = response.data;
    console.log(`获取到API响应: ${JSON.stringify(responseData)}`);

    if (responseData.status === "completed" && responseData.urls && responseData.urls.length > 0) {
      // 获取内容URL
      const contentUrl = responseData.urls[0];
      
      // 获取实际内容
      const contentResponse = await axios.get(contentUrl);
      
      // 返回处理后的数据
      return {
        status: 'success',
        title: responseData.title || '未知标题',
        data: contentResponse.data,
        originalUrl: url,
        contentType: format
      };
    } else {
      throw new Error(`API返回数据不符合预期: ${JSON.stringify(responseData)}`);
    }
  } catch (error: any) {
    console.error('获取文章内容失败:', error.message);
    throw error;
  }
}

// 最大重试次数
const MAX_RETRY_COUNT = 3;

// API路由处理
app.post('', async (c) => {
  let retryCount = 0;

  while (retryCount <= MAX_RETRY_COUNT) {
    try {
      const { url, format = 'text' } = await c.req.json();
      
      if (!url) {
        return c.json({
          status: 'error',
          message: '缺少URL参数'
        }, 400);
      }

      // 验证格式参数
      const contentFormat = ['text', 'markdown'].includes(format as string) 
        ? format as 'text' | 'markdown'
        : 'text';
      
      console.log(`处理请求: ${url}, 格式: ${contentFormat}, 尝试: ${retryCount + 1}/${MAX_RETRY_COUNT + 1}`);
      
      // 获取文章内容
      const result = await fetchArticleContent(url, contentFormat);
      
      return c.json(result);

    } catch (error: unknown) {
      retryCount++;
      
      let errorMessage = '未知错误';
      let errorCode = 'UNKNOWN_ERROR';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        
        if (axios.isAxiosError(error)) {
          const axiosError = error;
          
          if (axiosError.response) {
            errorCode = `HTTP_${axiosError.response.status}`;
            errorMessage = `服务器返回错误: [${axiosError.response.status}] ${axiosError.response.statusText || ''}`;
            
            if (axiosError.response.data) {
              try {
                const dataStr = typeof axiosError.response.data === 'object' 
                  ? JSON.stringify(axiosError.response.data)
                  : String(axiosError.response.data);
                errorMessage += `, 响应数据: ${dataStr.substring(0, 200)}${dataStr.length > 200 ? '...' : ''}`;
              } catch {
                errorMessage += ', 响应数据无法解析';
              }
            }
          } else if (axiosError.request) {
            errorCode = 'REQUEST_NO_RESPONSE';
            errorMessage = '已发送请求但未收到响应，可能是网络问题或目标服务器无响应';
          } else {
            errorCode = 'REQUEST_SETUP_ERROR';
            errorMessage = `请求设置错误: ${axiosError.message}`;
          }
        }
      }
      
      console.error(`错误 (尝试 ${retryCount}/${MAX_RETRY_COUNT}): ${errorCode} - ${errorMessage}`);
      
      // 如果已经达到最大重试次数，则返回错误
      if (retryCount > MAX_RETRY_COUNT) {
        return c.json({
          status: 'error',
          error: errorMessage,
          errorCode: errorCode,
          retriesAttempted: retryCount - 1,
          timestamp: new Date().toISOString()
        }, 500);
      }
      
      // 等待一秒再重试
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
});

export default app;