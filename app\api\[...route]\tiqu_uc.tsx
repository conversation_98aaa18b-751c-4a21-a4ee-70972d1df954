import puppeteer from 'puppeteer';
import { <PERSON>o } from 'hono';
import * as cheerio from 'cheerio';

const app = new Hono();

function cleanHTMLContent(html: string): { title: string; text: string } {
    const $ = cheerio.load(html);
    
    // 获取标题
    const title = $('.index-articleContainer-db6dcd14 h1').text().trim();
    
    // 获取正文内容
    let textContent = '';
    
    // 获取所有段落文本
    $('.index-articleSummary-064623c9 p').each((_, element) => {
        const $element = $(element);
        // 跳过图片段落
        if (!$element.hasClass('imgbox')) {
            const text = $element.text().trim();
            if (text) {
                textContent += text + '\n';
            }
        }
    });
    
    // 清理文本内容
    textContent = textContent
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, '\n')
        .trim();
    
    return { 
        title, 
        text: textContent 
    };
}

// API 路由处理
app.post('', async (c) => {
    try {
        const { url } = await c.req.json();

        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        });

        const page = await browser.newPage();

        await page.goto(url, {
            waitUntil: 'networkidle0',
            timeout: 60000
        });

        // 等待文章内容加载
        await page.waitForSelector('.index-articleContainer-db6dcd14', { timeout: 10000 });

        const html = await page.content();
        await browser.close();

        const { title, text } = cleanHTMLContent(html);

        return c.json({
            status: 'success',
            title: title,
            data: text,
        });

    } catch (error: unknown) {
        console.error('Scraping error:', error);
        return c.json({
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
        }, 500);
    }
});

export default app;