import puppeteer from 'puppeteer';
import { <PERSON><PERSON> } from 'hono';
import * as cheerio from 'cheerio';

const app = new Hono();

function cleanHTMLContent(html: string): { title: string; text: string } {
    const $ = cheerio.load(html);
    
    // 获取标题和内容
    const title = $('#detail-title').text().trim();
    const description = $('#detail-desc .note-text').text().trim();
    
    // 清理文本内容
    let textContent = description.replace(/\s+/g, ' ').trim();
    
    // 移除标签文本（以#开头的内容）
    textContent = textContent.replace(/#[^\s#]+/g, '').trim();
    
    return { 
        title, 
        text: textContent 
    };
}

app.post('', async (c) => {
    try {
        const { url } = await c.req.json();

        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox', 
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        });

        const page = await browser.newPage();

        // 设置用户代理
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        // 设置视口大小
        await page.setViewport({ width: 1920, height: 1080 });

        await page.goto(url, {
            waitUntil: 'networkidle0',
            timeout: 60000
        });

        // 等待内容加载
        await page.waitForSelector('.note-content', { timeout: 10000 });

        const html = await page.content();
        await browser.close();

        const { title, text } = cleanHTMLContent(html);

        return c.json({
            status: 'success',
            title,
            data: text
        });

    } catch (error: unknown) {
        console.error('爬取错误:', error);
        return c.json({
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
        }, 500);
    }
});

export default app;