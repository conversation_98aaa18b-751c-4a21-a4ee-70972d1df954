import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { SignJWT } from 'jose'

const secret = new TextEncoder().encode(process.env.JWT_SECRET!)

export async function POST(request: Request) {
  try {
    const { password } = await request.json()
    
    if (password === process.env.ADMIN_SECRET) {
      // 生成 JWT token
      const token = await new SignJWT({
        isAdmin: true,
      })
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt()
        .setExpirationTime('24h')
        .sign(secret)
      
      // 设置 cookie
      const cookieStore =await cookies()
      cookieStore.set('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 7 * 60 * 60 // 1天
      })
      
      return NextResponse.json(
        { success: true },
        { status: 200 }
      )
    }
    
    return NextResponse.json(
      { error: '密码错误' },
      { status: 401 }
    )
  } catch (error) {
    console.error('Auth error:', error)
    return NextResponse.json(
      { error: '验证失败' },
      { status: 500 }
    )
  }
} 