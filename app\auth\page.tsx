'use client'

import { useState } from 'react'
//import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

export default function AuthPage() {
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  //const router = useRouter()
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "验证成功",
          description: "正在跳转...",
        })
        
        setTimeout(() => {
          window.location.href = '/'
        }, 1000)
      } else {
        toast({
          title: "验证失败",
          description: data.error || "访问密码错误",
          variant: "destructive",
        })
      }
    } catch (_error) {
      const errorMessage = _error instanceof Error ? _error.message : "未知错误";
      toast({
        title: "错误",
        description: `验证过程发生错误: ${errorMessage}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-[350px]">
        <CardHeader>
          <CardTitle>访问验证</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="请输入访问密码"
                className="w-full p-2 border rounded-md"
                required
              />
            </div>
            <Button 
              type="submit" 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? '登录中...' : '登录'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 