version: '3'

services:
  wenzhang:
    image: skadli/wenzhang:latest
    restart: always
    init: true
    ports:
      - "6900:6900"
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PORT=6900
      - NEXT_PUBLIC_API_URL=http://localhost:6900
      - NEXT_TELEMETRY_DISABLED=1
    env_file:
      - .env
    volumes:
      - ./.env:/app/.env
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:6900"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - wenzhang

networks:
  wenzhang:
    driver: bridge

