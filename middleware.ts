import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtVerify } from 'jose'

const secret = new TextEncoder().encode(process.env.JWT_SECRET!)

export async function middleware(request: NextRequest) {
  console.log('当前路径:', request.nextUrl.pathname)
  
  const token = request.cookies.get('token')?.value
  console.log('当前token:', token)
  
  // 如果是auth相关的路径，直接放行
  if (request.nextUrl.pathname.startsWith('/auth')) {
    return NextResponse.next()
  }
  
  // 如果是API请求,检查token
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // auth接口不需要验证
    if (request.nextUrl.pathname === '/api/auth') {
      return NextResponse.next()
    }
    
    if (!token) {
      return new NextResponse(
        JSON.stringify({ error: '未授权访问' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    try {
      // 验证 JWT token
      await jwtVerify(token, secret)
      return NextResponse.next()
    } catch (error) {
      return new NextResponse(
        JSON.stringify({ error: 'token无效或已过期' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }
  }

  // 如果未验证且不是auth页面,重定向到auth页面
  if (!token && !request.nextUrl.pathname.startsWith('/auth')) {
    return NextResponse.redirect(new URL('/auth', request.url))
  }

  try {
    if (token) {
      const decoded = await jwtVerify(token, secret)
      console.log('token验证结果:', decoded)
    }
    return NextResponse.next()
  } catch (error) {
    console.error('token验证失败:', error)
    return NextResponse.redirect(new URL('/auth', request.url))
  }
}

// 配置需要进行中间件验证的路径
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
} 