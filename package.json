{"name": "demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 6900", "build": "next build", "start": "next start -p 6900", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^0.0.72", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@types/jsonwebtoken": "^9.0.7", "ai": "^3.4.33", "axios": "^1.8.4", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "hono": "^4.6.9", "https-proxy-agent": "^7.0.6", "jose": "^5.9.6", "lucide-react": "^0.456.0", "next": "15.0.3", "puppeteer": "^23.7.1", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^9.0.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "module": "index.ts", "type": "module"}